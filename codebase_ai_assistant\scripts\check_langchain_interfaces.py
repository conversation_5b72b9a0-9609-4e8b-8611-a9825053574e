#!/usr/bin/env python3
"""
LangChain接口查看工具
帮助开发者了解LangChain的接口和使用方法
"""
import sys
import inspect
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    # 导入LangChain相关模块
    from langchain.chat_models import ChatOpenAI
    from langchain.embeddings import OpenAIEmbeddings
    from langchain.schema import BaseMessage, HumanMessage, AIMessage
    from langchain.agents import AgentExecutor, create_openai_functions_agent
    from langchain.tools import BaseTool
    from langchain.prompts import ChatPromptTemplate
    from langchain.vectorstores import Chroma
    
    print("✅ LangChain模块导入成功!")
    
except ImportError as e:
    print(f"❌ LangChain模块导入失败: {e}")
    print("请确保已安装LangChain: pip install langchain")
    sys.exit(1)


def show_class_methods(cls, title):
    """显示类的方法和属性"""
    print(f"\n{'='*50}")
    print(f"📋 {title}")
    print(f"{'='*50}")
    
    # 获取类的文档字符串
    if cls.__doc__:
        print(f"📖 描述: {cls.__doc__.strip()}")
    
    print(f"\n🔧 主要方法:")
    methods = [method for method in dir(cls) if not method.startswith('_')]
    
    for method_name in sorted(methods):
        try:
            method = getattr(cls, method_name)
            if callable(method):
                # 获取方法签名
                try:
                    sig = inspect.signature(method)
                    print(f"  • {method_name}{sig}")
                    
                    # 获取方法文档
                    if method.__doc__:
                        doc_lines = method.__doc__.strip().split('\n')
                        if doc_lines:
                            print(f"    └─ {doc_lines[0]}")
                except:
                    print(f"  • {method_name}(...)")
            else:
                # 属性
                print(f"  ◦ {method_name} (属性)")
        except:
            continue


def show_langchain_interfaces():
    """显示LangChain主要接口"""
    
    print("🚀 LangChain 接口文档查看器")
    print("="*60)
    
    # 1. 聊天模型接口
    show_class_methods(ChatOpenAI, "ChatOpenAI - 聊天模型接口")
    
    # 2. 嵌入模型接口
    show_class_methods(OpenAIEmbeddings, "OpenAIEmbeddings - 嵌入模型接口")
    
    # 3. 消息接口
    show_class_methods(BaseMessage, "BaseMessage - 消息基类")
    show_class_methods(HumanMessage, "HumanMessage - 人类消息")
    show_class_methods(AIMessage, "AIMessage - AI消息")
    
    # 4. 工具接口
    show_class_methods(BaseTool, "BaseTool - 工具基类")
    
    # 5. 向量存储接口
    show_class_methods(Chroma, "Chroma - 向量数据库")
    
    # 6. 提示模板接口
    show_class_methods(ChatPromptTemplate, "ChatPromptTemplate - 聊天提示模板")


def show_usage_examples():
    """显示使用示例"""
    print(f"\n{'='*60}")
    print("💡 LangChain 使用示例")
    print(f"{'='*60}")
    
    examples = {
        "聊天模型调用": '''
from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage

# 创建聊天模型
llm = ChatOpenAI(model="gpt-4", temperature=0.1)

# 调用模型
response = llm.invoke([HumanMessage(content="Hello, world!")])
print(response.content)
        ''',
        
        "嵌入模型使用": '''
from langchain.embeddings import OpenAIEmbeddings

# 创建嵌入模型
embeddings = OpenAIEmbeddings()

# 嵌入文本
vectors = embeddings.embed_documents(["Hello", "World"])
query_vector = embeddings.embed_query("Hello")
        ''',
        
        "工具定义": '''
from langchain.tools import BaseTool

class MyTool(BaseTool):
    name = "my_tool"
    description = "这是我的工具"
    
    def _run(self, query: str) -> str:
        return f"处理查询: {query}"
        ''',
        
        "代理创建": '''
from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain.prompts import ChatPromptTemplate

# 创建提示模板
prompt = ChatPromptTemplate.from_messages([
    ("system", "你是一个有用的助手"),
    ("human", "{input}"),
    ("placeholder", "{agent_scratchpad}")
])

# 创建代理
agent = create_openai_functions_agent(llm, tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=tools)

# 执行任务
result = agent_executor.invoke({"input": "你好"})
        '''
    }
    
    for title, code in examples.items():
        print(f"\n📝 {title}:")
        print("-" * 40)
        print(code.strip())


def show_interface_compatibility():
    """显示接口兼容性说明"""
    print(f"\n{'='*60}")
    print("🔄 GLM接口兼容性说明")
    print(f"{'='*60}")
    
    compatibility_info = """
为了让GLM模型兼容LangChain接口，我们需要实现以下方法：

🎯 聊天模型兼容性:
  • invoke(messages) -> 调用模型生成响应
  • 支持消息格式: [{"role": "user", "content": "..."}]
  • 返回格式: 包含content属性的响应对象

🎯 嵌入模型兼容性:
  • embed_documents(texts) -> 嵌入文档列表
  • embed_query(text) -> 嵌入单个查询
  • 返回格式: 浮点数列表

🎯 消息格式转换:
  • LangChain消息 -> GLM消息格式
  • 角色映射: human->user, ai->assistant
  • 内容提取和格式化

🎯 错误处理:
  • API调用异常处理
  • 网络错误重试
  • 格式验证
    """
    
    print(compatibility_info)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="LangChain接口查看工具")
    parser.add_argument("--interfaces", "-i", action="store_true", help="显示接口文档")
    parser.add_argument("--examples", "-e", action="store_true", help="显示使用示例")
    parser.add_argument("--compatibility", "-c", action="store_true", help="显示兼容性说明")
    parser.add_argument("--all", "-a", action="store_true", help="显示所有信息")
    
    args = parser.parse_args()
    
    if args.all or not any([args.interfaces, args.examples, args.compatibility]):
        # 默认显示所有信息
        show_langchain_interfaces()
        show_usage_examples()
        show_interface_compatibility()
    else:
        if args.interfaces:
            show_langchain_interfaces()
        if args.examples:
            show_usage_examples()
        if args.compatibility:
            show_interface_compatibility()
    
    print(f"\n{'='*60}")
    print("📚 更多信息:")
    print("  • 官方文档: https://python.langchain.com/docs/")
    print("  • API参考: https://api.python.langchain.com/")
    print("  • GitHub: https://github.com/langchain-ai/langchain")
    print(f"{'='*60}")


if __name__ == "__main__":
    main()
